import { Injectable } from '@nestjs/common';
import { PrismaService } from '../notification/prisma.service';

type Campaign = {
  id: string;
  title: string;
  description: string;
  advertiserId: string;
  startDate: Date;
  endDate: Date;
  status: string;
  createdAt: Date;
  updatedAt: Date;
};

@Injectable()
export class CampaignService {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: any): Promise<Campaign> {
    // Accept CreateCampaignDto, add status before saving
    const campaignData = {
      ...data,
      status: 'draft', // or 'active' as your business logic requires
    };
    return this.prisma.campaign.create({ data: campaignData });
  }

  async findAll(): Promise<Campaign[]> {
    return this.prisma.campaign.findMany();
  }

  async findOne(id: string): Promise<Campaign | null> {
    return this.prisma.campaign.findUnique({ where: { id } });
  }

  async update(id: string, data: Partial<Omit<Campaign, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Campaign> {
    return this.prisma.campaign.update({ where: { id }, data });
  }

  async remove(id: string): Promise<Campaign> {
    return this.prisma.campaign.delete({ where: { id } });
  }
}
