import { Test, TestingModule } from '@nestjs/testing';
import { CampaignController } from './campaign.controller';
import { CampaignService } from './campaign.service';

describe('CampaignController', () => {
  let controller: CampaignController;

  const mockCampaignService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CampaignController],
      providers: [{ provide: CampaignService, useValue: mockCampaignService }],
    }).compile();

    controller = module.get<CampaignController>(CampaignController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should create a campaign', async () => {
    const dto = {
      title: 'A',
      description: 'B',
      advertiserId: 'u',
      startDate: new Date().toISOString(),
      endDate: new Date().toISOString(),
    };
    const campaign = { id: '1', ...dto, status: 'draft' };
    mockCampaignService.create.mockResolvedValue(campaign);
    await expect(controller.create(dto)).resolves.toEqual(campaign);
  });

  it('should return all campaigns', async () => {
    const campaigns = [{ id: '1' }];
    mockCampaignService.findAll.mockResolvedValue(campaigns);
    await expect(controller.findAll()).resolves.toEqual(campaigns);
  });

  it('should return one campaign', async () => {
    const campaign = { id: '1' };
    mockCampaignService.findOne.mockResolvedValue(campaign);
    await expect(controller.findOne('1')).resolves.toEqual(campaign);
  });

  it('should update a campaign', async () => {
    const campaign = { id: '1', title: 'B' };
    mockCampaignService.update.mockResolvedValue(campaign);
    await expect(controller.update('1', { title: 'B' })).resolves.toEqual(
      campaign,
    );
  });

  it('should remove a campaign', async () => {
    const campaign = { id: '1' };
    mockCampaignService.remove.mockResolvedValue(campaign);
    await expect(controller.remove('1')).resolves.toEqual(campaign);
  });
});
