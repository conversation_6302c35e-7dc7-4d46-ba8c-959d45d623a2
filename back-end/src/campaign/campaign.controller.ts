import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CampaignService } from './campaign.service';
import { Roles } from '../auth/roles.decorator';
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { CreateCampaignDto } from './create-campaign.dto';

@UseGuards(AuthGuard('jwt'))
@ApiTags('Campaign')
@ApiBearerAuth()
@Controller('campaign')
export class CampaignController {
  constructor(private readonly campaignService: CampaignService) {}

  @Post()
  @Roles('spteam')
  @ApiOperation({
    summary: 'Create a new campaign',
    description:
      'Only users with the "spteam" role are authorized to create campaigns. Requires Bearer JWT authentication.',
  })
  @ApiResponse({
    status: 201,
    description:
      'Campaign created successfully. Only accessible by users with the "spteam" role.',
    type: CreateCampaignDto,
  })
  create(@Body() data: CreateCampaignDto) {
    return this.campaignService.create(data);
  }

  @Get()
  @ApiOperation({ summary: 'Get all campaigns' })
  @ApiResponse({ status: 200, description: 'List of campaigns' })
  findAll() {
    return this.campaignService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get campaign by ID' })
  @ApiResponse({ status: 200, description: 'Campaign found' })
  findOne(@Param('id') id: string) {
    return this.campaignService.findOne(id);
  }

  @Patch(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Update campaign by ID' })
  @ApiResponse({ status: 200, description: 'Campaign updated' })
  update(@Param('id') id: string, @Body() data: any) {
    return this.campaignService.update(id, data);
  }

  @Delete(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Delete campaign by ID' })
  @ApiResponse({ status: 200, description: 'Campaign deleted' })
  remove(@Param('id') id: string) {
    return this.campaignService.remove(id);
  }
}
