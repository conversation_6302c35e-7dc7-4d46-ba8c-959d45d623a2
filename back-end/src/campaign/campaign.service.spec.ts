import { Test, TestingModule } from '@nestjs/testing';
import { CampaignService } from './campaign.service';

jest.mock('@prisma/client', () => {
  const mCampaign = {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };
  return {
    PrismaClient: jest.fn(() => ({ campaign: mCampaign })),
  };
});

describe('CampaignService', () => {
  let service: CampaignService;
  let prisma: any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CampaignService],
    }).compile();
    service = module.get<CampaignService>(CampaignService);
    prisma = (service as any).prisma.campaign;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should create a campaign', async () => {
    const campaign = {
      id: '1',
      title: 'A',
      description: 'B',
      advertiserId: 'u',
      startDate: new Date(),
      endDate: new Date(),
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    prisma.create.mockResolvedValue(campaign);
    await expect(service.create(campaign)).resolves.toEqual(campaign);
  });

  it('should find all campaigns', async () => {
    const campaigns = [{ id: '1' }];
    prisma.findMany.mockResolvedValue(campaigns);
    await expect(service.findAll()).resolves.toEqual(campaigns);
  });

  it('should find one campaign', async () => {
    const campaign = { id: '1' };
    prisma.findUnique.mockResolvedValue(campaign);
    await expect(service.findOne('1')).resolves.toEqual(campaign);
  });

  it('should update a campaign', async () => {
    const campaign = { id: '1', title: 'B' };
    prisma.update.mockResolvedValue(campaign);
    await expect(service.update('1', { title: 'B' })).resolves.toEqual(
      campaign,
    );
  });

  it('should remove a campaign', async () => {
    const campaign = { id: '1' };
    prisma.delete.mockResolvedValue(campaign);
    await expect(service.remove('1')).resolves.toEqual(campaign);
  });
});
