import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { RoleService } from './role.service';
import { Roles } from '../auth/roles.decorator';
import { AuthGuard } from '@nestjs/passport';

export class CreateRoleDto {
  name: string;
  description?: string;
  permissions: string[];
}

export class RoleDto {
  id: string;
  name: string;
  description?: string;
  permissions: string[];
}

@UseGuards(AuthGuard('jwt'))
@ApiTags('Role')
@ApiBearerAuth()
@Controller('role')
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  @Post()
  @Roles('admin')
  @ApiOperation({ summary: 'Create a new role' })
  @ApiResponse({ status: 201, description: 'Role created' })
  create(@Body() data: CreateRoleDto) {
    return this.roleService.create({
      name: data.name,
      permissions: data.permissions ?? [],
      description: data.description ?? ''
    });
  }

  @Get()
  @Roles('admin')
  @ApiOperation({ summary: 'Get all roles' })
  @ApiResponse({ status: 200, description: 'List of roles' })
  findAll() {
    return this.roleService.findAll();
  }

  @Get(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Get role by ID' })
  @ApiResponse({ status: 200, description: 'Role found' })
  findOne(@Param('id') id: string) {
    return this.roleService.findOne(id);
  }

  @Patch(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Update role by ID' })
  @ApiResponse({ status: 200, description: 'Role updated' })
  update(@Param('id') id: string, @Body() data: Partial<RoleDto>) {
    return this.roleService.update(id, {
      ...data,
      permissions: data.permissions ?? [],
      description: data.description ?? ''
    });
  }

  @Delete(':id')
  @Roles('admin')
  @ApiOperation({ summary: 'Delete role by ID' })
  @ApiResponse({ status: 200, description: 'Role deleted' })
  remove(@Param('id') id: string) {
    return this.roleService.remove(id);
  }
}
