import { Test, TestingModule } from '@nestjs/testing';
import { RoleController } from './role.controller';
import { RoleService } from './role.service';
import { CreateRoleDto } from './role.controller';

describe('RoleController', () => {
  let controller: RoleController;
  let service: RoleService;

  const mockRoleService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RoleController],
      providers: [
        { provide: RoleService, useValue: mockRoleService },
      ],
    }).compile();

    controller = module.get<RoleController>(RoleController);
    service = module.get<RoleService>(RoleService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should create a role', async () => {
    const dto: CreateRoleDto = { name: 'admin', description: 'Administrator', permissions: ['manage_users'] };
    const role = { id: '1', ...dto };
    mockRoleService.create.mockResolvedValue(role);
    await expect(controller.create(dto)).resolves.toEqual(role);
  });

  it('should return all roles', async () => {
    const roles = [{ id: '1', name: 'admin' }];
    mockRoleService.findAll.mockResolvedValue(roles);
    await expect(controller.findAll()).resolves.toEqual(roles);
  });

  it('should return one role', async () => {
    const role = { id: '1', name: 'admin' };
    mockRoleService.findOne.mockResolvedValue(role);
    await expect(controller.findOne('1')).resolves.toEqual(role);
  });

  it('should update a role', async () => {
    const role = { id: '1', name: 'user', description: 'User', permissions: ['read'] };
    mockRoleService.update.mockResolvedValue(role);
    await expect(controller.update('1', { name: 'user', description: 'User', permissions: ['read'] })).resolves.toEqual(role);
  });

  it('should remove a role', async () => {
    const role = { id: '1', name: 'admin' };
    mockRoleService.remove.mockResolvedValue(role);
    await expect(controller.remove('1')).resolves.toEqual(role);
  });
});
