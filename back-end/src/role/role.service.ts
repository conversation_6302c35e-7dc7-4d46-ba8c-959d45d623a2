import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

// <PERSON><PERSON>nh nghĩa DTO cho tạo role mới
export type CreateRoleDto = {
  name: string;
  description: string | null;
  permissions: string[];
};

type Role = {
  id: string;
  name: string;
  description: string | null;
  permissions: string[];
};

@Injectable()
export class RoleService {
  private prisma = new PrismaClient();

  async create(data: CreateRoleDto & { id?: string }): Promise<Role> {
    const id = data.id ?? uuidv4();
    return this.prisma.role.create({ data: { ...data, id } });
  }

  async findAll(): Promise<Role[]> {
    return this.prisma.role.findMany();
  }

  async findOne(id: string): Promise<Role | null> {
    return this.prisma.role.findUnique({ where: { id } });
  }

  async update(id: string, data: Partial<Omit<Role, 'id'>>): Promise<Role> {
    return this.prisma.role.update({ where: { id }, data });
  }

  async remove(id: string): Promise<Role> {
    return this.prisma.role.delete({ where: { id } });
  }
}
