import { Test, TestingModule } from '@nestjs/testing';
import { RoleService, CreateRoleDto } from './role.service';

jest.mock('@prisma/client', () => {
  const mRole = {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };
  return {
    PrismaClient: jest.fn(() => ({ role: mRole })),
  };
});

describe('RoleService', () => {
  let service: RoleService;
  let prisma: any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [RoleService],
    }).compile();
    service = module.get<RoleService>(RoleService);
    prisma = (service as any).prisma.role;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should create a role', async () => {
    const role = { id: '1', name: 'admin', description: 'Administrator', permissions: ['manage_users'] };
    prisma.create.mockResolvedValue(role);
    const createDto: CreateRoleDto = { name: 'admin', description: 'Administrator', permissions: ['manage_users'] };
    await expect(service.create(createDto)).resolves.toEqual(role);
  });

  it('should find all roles', async () => {
    const roles = [{ id: '1', name: 'admin' }];
    prisma.findMany.mockResolvedValue(roles);
    await expect(service.findAll()).resolves.toEqual(roles);
  });

  it('should find one role', async () => {
    const role = { id: '1', name: 'admin' };
    prisma.findUnique.mockResolvedValue(role);
    await expect(service.findOne('1')).resolves.toEqual(role);
  });

  it('should update a role', async () => {
    const role = { id: '1', name: 'user' };
    prisma.update.mockResolvedValue(role);
    await expect(service.update('1', { name: 'user' })).resolves.toEqual(role);
  });

  it('should remove a role', async () => {
    const role = { id: '1', name: 'admin' };
    prisma.delete.mockResolvedValue(role);
    await expect(service.remove('1')).resolves.toEqual(role);
  });
});
